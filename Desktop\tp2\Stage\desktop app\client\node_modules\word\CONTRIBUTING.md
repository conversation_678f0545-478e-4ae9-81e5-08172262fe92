# Contributing

The WordJS Libraries should be free and clear to use in your projects.  In
order to maintain that, every contributor must be vigilant.

There have been many projects in the past that have been very lax regarding
licensing, and we are of the opinion that those are ticking timebombs and that
no corporate product should depend on them.  


# Required Reading

These are pretty short reads and emphasize the importance of proper licensing:

- https://github.com/kennethreitz/tablib/issues/114 (discussion of other tools)

- http://www.codinghorror.com/blog/2007/04/pick-a-license-any-license.html


# Raising Issues

Issues should generally be accompanied by test files.  Since github does not
support attachments, the best method is to send files to <<EMAIL>>
(subject line should contain issue number or message) or to share using some
storage service.  Unless expressly permitted, any attachments will not be
shared or included in a test suite (although I will ask :)

# Pre-Contribution Checklist

Before thinking about contributing, make sure that:

- You are not, nor have ever been, an employee of Microsoft Corporation.

- You have not signed any NDAs or Shared Source Agreements with Microsoft 
  Corporation or a subsidiary

- You have not consulted any existing relevant codebase (if you have, please
take note of which codebases were consulted).

If you cannot attest to each of these items, the best approach is to raise an
issue.  If it is a particularly high-priority issue, please drop an email to
<<EMAIL>> and it will be prioritized.


# Intra-Contribution

Keep these in mind as you work:

- Your contributions are your original work.  Take note of any resources you
  consult in the process (and be extra careful not to use unlicensed code on
  the internet.

- You are working on your own time.  Unless they explicitly grant permission, 
  your employer may be the ultimate owner of your IP


# Post-Contribution 

Before contributions are merged, you will receive an email (at the address
associated with the git commit) and will be asked to confirm the aforementioned
items.
