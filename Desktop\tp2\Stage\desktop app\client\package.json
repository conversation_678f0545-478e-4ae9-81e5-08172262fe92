{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1", "react-select": "^5.10.1", "react-toastify": "^9.1.3", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^5.0.0"}}