import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import ModernLayout from '../../components/Layout/ModernLayout';
import { Link } from 'react-router-dom';
import * as XLSX from 'xlsx';
import {
  FiCheckCircle,
  FiFileText,
  FiClock,
  FiAlertCircle,
  FiUser,
  FiMapPin,
  FiSearch,
  FiUpload,
  FiEdit,
  FiSave
} from 'react-icons/fi';

const ReceiveReadingSheets = () => {
  const [readingSheets, setReadingSheets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    assigned: 0,
    completed: 0
  });

  // Search and import states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [importedData, setImportedData] = useState([]);
  const [showImportTable, setShowImportTable] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [importing, setImporting] = useState(false);

  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    const fetchReadingSheets = async () => {
      try {
        const res = await api.get('/api/reading-sheets');
        const allSheets = res.data.data || [];

        // Filter for assigned sheets that are not completed
        const assignedSheets = allSheets.filter(
          sheet => sheet.status === 'assigned'
        );

        // If not admin, only show sheets assigned to the current user
        const filteredSheets = isAdmin
          ? assignedSheets
          : assignedSheets.filter(sheet => sheet.assignedTo?._id === user.id);

        setReadingSheets(filteredSheets);

        // Calculate statistics
        setStats({
          total: allSheets.length,
          assigned: assignedSheets.length,
          completed: allSheets.filter(sheet => sheet.status === 'completed').length
        });

        setLoading(false);
      } catch (error) {
        toast.error('Échec du chargement des fiches de relevé');
        setLoading(false);
      }
    };

    fetchReadingSheets();
  }, [isAdmin, user]);

  const markAsCompleted = async (id) => {
    try {
      await api.put(`/api/reading-sheets/${id}/complete`);

      // Update reading sheets list
      setReadingSheets(readingSheets.filter(sheet => sheet._id !== id));

      // Update statistics
      setStats({
        ...stats,
        assigned: stats.assigned - 1,
        completed: stats.completed + 1
      });

      toast.success('Fiche de relevé marquée comme complétée');
    } catch (error) {
      toast.error('Échec de la mise à jour de la fiche de relevé');
    }
  };

  // Search functionality
  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      toast.error('Veuillez entrer un numéro de fiche');
      return;
    }

    setLoading(true);
    try {
      // Search using the API with search parameter
      const response = await api.get(`/api/reading-sheets?search=${encodeURIComponent(searchTerm)}`);
      const foundSheets = response.data.data || [];

      if (foundSheets.length > 0) {
        // Take the first match (most relevant)
        setSelectedSheet(foundSheets[0]);
        toast.success(`Fiche trouvée: ${foundSheets[0].sheetNumber}`);
      } else {
        toast.error('Aucune fiche trouvée avec ce numéro');
        setSelectedSheet(null);
      }
    } catch (error) {
      console.error('Error searching for sheet:', error);
      toast.error('Erreur lors de la recherche');
      setSelectedSheet(null);
    } finally {
      setLoading(false);
    }
  };

  // File import functionality
  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!selectedSheet) {
      toast.error('Veuillez d\'abord rechercher et sélectionner une fiche');
      return;
    }

    setImporting(true);
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Process and compare with subscribers
        await processImportedData(jsonData);
      } catch (error) {
        console.error('Error reading file:', error);
        toast.error('Erreur lors de la lecture du fichier');
      } finally {
        setImporting(false);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  // Process imported data and compare with subscribers
  const processImportedData = async (data) => {
    try {
      // Get subscribers for the selected sheet's circuit
      const circuitInfo = selectedSheet.circuit;
      if (!circuitInfo) {
        toast.error('Informations de circuit manquantes pour cette fiche');
        return;
      }

      const subscribersRes = await api.get(`/api/subscribers/circuit/${circuitInfo.groupe}/${circuitInfo.tournee}/${circuitInfo.circuit_number}`);
      const subscribers = subscribersRes.data.data || [];

      // Map imported data to match with subscribers
      const processedData = data.map(row => {
        // Find matching subscriber by reference or meter number
        const matchingSubscriber = subscribers.find(sub =>
          sub.reference === row['Référence'] ||
          sub.meter_number === row['N° de compteur']
        );

        return {
          id: Math.random().toString(36).substring(2, 11),
          codeAgence: row['Code d\'agence'] || '',
          groupe: row['Groupe'] || '',
          tournee: row['Tournée'] || '',
          circuit: row['Circuit'] || '',
          reference: row['Référence'] || '',
          client: row['Client'] || '',
          adresse: row['Adresse'] || '',
          trf: row['TRF'] || '',
          cad: row['CAD(dials)'] || '',
          meterNumber: row['N° de compteur'] || '',
          meterType: row['Type de compteur'] || '',
          oldIndex: row['Ancien index(index)'] || '',
          newIndex: row['Nouvel index'] || '', // Editable
          salesRepRef: row['N° de attaché'] || '',
          anomalies: row['Anomalies'] || '',
          observation: row['Observation'] || '', // Editable
          // Database match info
          dbMatch: matchingSubscriber ? true : false,
          dbData: matchingSubscriber || null
        };
      });

      setImportedData(processedData);
      setEditableData(processedData);
      setShowImportTable(true);
      toast.success(`${processedData.length} lignes importées et comparées avec la base de données`);
    } catch (error) {
      console.error('Error processing imported data:', error);
      toast.error('Erreur lors du traitement des données importées');
    }
  };

  // Handle editing of table data
  const handleDataEdit = (id, field, value) => {
    setEditableData(prevData =>
      prevData.map(row =>
        row.id === id ? { ...row, [field]: value } : row
      )
    );
  };

  // Save readings to database
  const handleSaveReadings = async () => {
    try {
      setImporting(true);

      // Prepare readings data for saving
      const readingsToSave = editableData.map(row => ({
        meterId: row.meterNumber,
        customerId: row.reference,
        customerName: row.client,
        readingValue: parseFloat(row.newIndex) || 0,
        readingSheet: selectedSheet._id,
        notes: row.observation,
        validationMode: 'offline'
      }));

      // Save all readings
      const savePromises = readingsToSave.map(reading =>
        api.post('/api/readings', reading)
      );

      await Promise.all(savePromises);

      toast.success(`${readingsToSave.length} relevés sauvegardés avec succès`);

      // Reset import state
      setShowImportTable(false);
      setImportedData([]);
      setEditableData([]);
      setSelectedSheet(null);
      setSearchTerm('');

    } catch (error) {
      console.error('Error saving readings:', error);
      toast.error('Erreur lors de la sauvegarde des relevés');
    } finally {
      setImporting(false);
    }
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="flex items-center justify-center h-full min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="space-y-4">
  

        {/* Search Section */}
        <div className="bg-white border border-gray-200 shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
            <FiSearch className="mr-2 h-5 w-5 text-primary-600" />
            Rechercher une Fiche de Relevé
          </h3>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                Numéro de Fiche
              </label>
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Entrer le numéro de fiche..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <button
              onClick={handleSearch}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200 rounded-md"
            >
              <FiSearch className="mr-2 h-4 w-4" />
              Rechercher
            </button>
          </div>

          {/* Selected Sheet Display */}
          {selectedSheet && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h4 className="text-sm font-medium text-green-800 mb-2">Fiche Sélectionnée</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Numéro:</span>
                  <span className="ml-2 text-gray-900">{selectedSheet.sheetNumber}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Zone:</span>
                  <span className="ml-2 text-gray-900">{selectedSheet.area}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Assigné à:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedSheet.assignedTo ? `${selectedSheet.assignedTo.firstName} ${selectedSheet.assignedTo.lastName}` : 'Non assigné'}
                  </span>
                </div>
              </div>

              {/* File Import Section */}
              <div className="mt-4 pt-4 border-t border-green-200">
                <label htmlFor="file-import" className="block text-sm font-medium text-gray-700 mb-2">
                  Importer le fichier de relevés (CSV/Excel)
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="file"
                    id="file-import"
                    accept=".csv,.xlsx,.xls"
                    onChange={handleFileImport}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                    disabled={importing}
                  />
                  {importing && (
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary-500 mr-2"></div>
                      Importation en cours...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Statistics Cards - Formal Style */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-white p-5 border border-gray-200 shadow-sm">
            <div className="flex items-center">
              <div className="p-3 bg-gray-100 mr-3 rounded">
                <FiFileText className="h-5 w-5 text-primary-600" />
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Total Fiches</p>
                <p className="text-lg font-medium text-gray-800">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-5 border border-gray-200 shadow-sm">
            <div className="flex items-center">
              <div className="p-3 bg-gray-100 mr-3 rounded">
                <FiClock className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Fiches Assignées</p>
                <p className="text-lg font-medium text-gray-800">{stats.assigned}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-5 border border-gray-200 shadow-sm">
            <div className="flex items-center">
              <div className="p-3 bg-gray-100 mr-3 rounded">
                <FiCheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Fiches Complétées</p>
                <p className="text-lg font-medium text-gray-800">{stats.completed}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Import Table */}
        {showImportTable && (
          <div className="bg-white border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <FiUpload className="mr-2 h-5 w-5 text-primary-600" />
                  Données Importées - {editableData.length} relevés
                </h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowImportTable(false)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200 rounded-md"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleSaveReadings}
                    disabled={importing}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium hover:bg-green-700 transition-colors duration-200 rounded-md disabled:opacity-50"
                  >
                    {importing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Sauvegarde...
                      </>
                    ) : (
                      <>
                        <FiSave className="mr-2 h-4 w-4" />
                        Sauvegarder les Relevés
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Référence</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Client</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">N° Compteur</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ancien Index</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nouvel Index</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">Observation</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {editableData.map((row) => (
                    <tr key={row.id} className={`hover:bg-gray-50 ${row.dbMatch ? 'bg-green-50' : 'bg-red-50'}`}>
                      <td className="px-3 py-3 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          row.dbMatch
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {row.dbMatch ? 'Trouvé' : 'Non trouvé'}
                        </span>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900">
                        {row.reference}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900">
                        {row.client}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900">
                        {row.meterNumber}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900">
                        {row.oldIndex}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap">
                        <input
                          type="number"
                          value={row.newIndex}
                          onChange={(e) => handleDataEdit(row.id, 'newIndex', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Nouvel index"
                        />
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap">
                        <input
                          type="text"
                          value={row.observation}
                          onChange={(e) => handleDataEdit(row.id, 'observation', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Observation"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="bg-white border border-gray-200 overflow-hidden shadow-sm">
          {readingSheets.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="bg-blue-50 rounded-full p-3 mb-4">
                <FiAlertCircle className="h-8 w-8 text-blue-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Aucune fiche à recevoir</h3>
              <p className="text-gray-500 text-center max-w-md mb-4">
                Il n'y a pas de fiches de relevé assignées disponibles pour la réception.
              </p>
              <Link
                to="/reading-sheets"
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
              >
                <FiFileText className="mr-2 h-4 w-4" />
                Voir toutes les fiches
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Numéro</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Zone</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Assigné à</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date d'assignation</th>
                    <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {readingSheets.map(sheet => (
                    <tr key={sheet._id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiFileText className="h-4 w-4 text-gray-400 mr-2" />
                          <div className="text-sm font-medium text-gray-800">{sheet.sheetNumber}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiMapPin className="h-4 w-4 text-gray-400 mr-2" />
                          <div className="text-sm text-gray-700">{sheet.area}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiUser className="h-4 w-4 text-gray-400 mr-2" />
                          <div className="text-sm text-gray-700">
                            {sheet.assignedTo ? sheet.assignedTo.name || `${sheet.assignedTo.firstName || ''} ${sheet.assignedTo.lastName || ''}`.trim() : 'Non assignée'}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-gray-700">
                          {sheet.assignedDate
                            ? new Date(sheet.assignedDate).toLocaleDateString('fr-FR')
                            : 'N/A'}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right">
                        <button
                          onClick={() => markAsCompleted(sheet._id)}
                          className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium hover:bg-green-700 transition-colors duration-200"
                        >
                          <FiCheckCircle className="mr-1.5 h-4 w-4" />
                          Marquer comme complétée
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </ModernLayout>
  );
};

export default ReceiveReadingSheets;
