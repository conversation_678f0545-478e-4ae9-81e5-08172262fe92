import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import ModernLayout from '../../components/Layout/ModernLayout';
import { Link } from 'react-router-dom';
import * as XLSX from 'xlsx';
import {
  FiCheckCircle,
  FiFileText,
  FiClock,
  FiAlertCircle,
  FiUser,
  FiMapPin,
  FiSearch,
  FiUpload,
  FiEdit,
  FiSave,
  FiFilter,
  FiX
} from 'react-icons/fi';

const ReceiveReadingSheets = () => {
  const [readingSheets, setReadingSheets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    assigned: 0,
    completed: 0
  });

  // Search and import states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [importedData, setImportedData] = useState([]);
  const [showImportTable, setShowImportTable] = useState(false);
  const [editableData, setEditableData] = useState([]);
  const [importing, setImporting] = useState(false);

  // Filter states
  const [showOnlyAnomalies, setShowOnlyAnomalies] = useState(false);
  const [clientFilter, setClientFilter] = useState('');

  // Pagination state for import table
  const [importPage, setImportPage] = useState(1);
  const clientsPerPage = 20;

  // Filter data based on anomalies and client filters
  const filteredData = editableData.filter(row => {
    const matchesAnomalies = !showOnlyAnomalies || (row.anomalies && row.anomalies.trim() !== '');

    const matchesClient = !clientFilter ||
      row.client.toLowerCase().includes(clientFilter.toLowerCase()) ||
      row.meterNumber.toLowerCase().includes(clientFilter.toLowerCase()) ||
      row.reference.toLowerCase().includes(clientFilter.toLowerCase());

    return matchesAnomalies && matchesClient;
  });

  const totalImportPages = Math.ceil(filteredData.length / clientsPerPage);
  const paginatedEditableData = filteredData.slice((importPage - 1) * clientsPerPage, importPage * clientsPerPage);

  function renderPagination() {
    if (totalImportPages <= 1) return null;
    const pageNumbers = [];
    for (let i = 1; i <= totalImportPages; i++) {
      pageNumbers.push(i);
    }
    return (
      <div className="flex justify-center items-center gap-2 my-4">
        <button
          className="px-2 py-1 border border-gray-300 rounded disabled:opacity-50"
          onClick={() => setImportPage(1)}
          disabled={importPage === 1}
          title="Première page"
        >
          &#171;
        </button>
        <button
          className="px-2 py-1 border border-gray-300 rounded disabled:opacity-50"
          onClick={() => setImportPage(p => Math.max(1, p - 1))}
          disabled={importPage === 1}
          title="Page précédente"
        >
          &#8249;
        </button>
        {pageNumbers.map(num => (
          <button
            key={num}
            className={`px-3 py-1 border border-gray-300 rounded ${importPage === num ? 'bg-gray-200 font-bold' : 'bg-white'} transition-colors`}
            onClick={() => setImportPage(num)}
          >
            {num}
          </button>
        ))}
        <button
          className="px-2 py-1 border border-gray-300 rounded disabled:opacity-50"
          onClick={() => setImportPage(p => Math.min(totalImportPages, p + 1))}
          disabled={importPage === totalImportPages}
          title="Page suivante"
        >
          &#8250;
        </button>
        <button
          className="px-2 py-1 border border-gray-300 rounded disabled:opacity-50"
          onClick={() => setImportPage(totalImportPages)}
          disabled={importPage === totalImportPages}
          title="Dernière page"
        >
          &#187;
        </button>
      </div>
    );
  }

  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    const fetchReadingSheets = async () => {
      try {
        const res = await api.get('/api/reading-sheets');
        const allSheets = res.data.data || [];

        // Filter for assigned sheets that are not completed
        const assignedSheets = allSheets.filter(
          sheet => sheet.status === 'assigned'
        );

        // If not admin, only show sheets assigned to the current user
        const filteredSheets = isAdmin
          ? assignedSheets
          : assignedSheets.filter(sheet => sheet.assignedTo?._id === user.id);

        setReadingSheets(filteredSheets);

        // Calculate statistics
        setStats({
          total: allSheets.length,
          assigned: assignedSheets.length,
          completed: allSheets.filter(sheet => sheet.status === 'completed').length
        });

        setLoading(false);
      } catch (error) {
        toast.error('Échec du chargement des fiches de relevé');
        setLoading(false);
      }
    };

    fetchReadingSheets();
  }, [isAdmin, user]);

  const markAsCompleted = async (id) => {
    try {
      await api.put(`/api/reading-sheets/${id}/complete`);

      // Update reading sheets list
      setReadingSheets(readingSheets.filter(sheet => sheet._id !== id));

      // Update statistics
      setStats({
        ...stats,
        assigned: stats.assigned - 1,
        completed: stats.completed + 1
      });

      toast.success('Fiche de relevé marquée comme complétée');
    } catch (error) {
      toast.error('Échec de la mise à jour de la fiche de relevé');
    }
  };

  // Search functionality
  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      toast.error('Veuillez entrer un numéro de fiche');
      return;
    }

    setLoading(true);
    try {
      // Search using the API with search parameter
      const response = await api.get(`/api/reading-sheets?search=${encodeURIComponent(searchTerm)}`);
      const foundSheets = response.data.data || [];

      if (foundSheets.length > 0) {
        // Take the first match (most relevant)
        setSelectedSheet(foundSheets[0]);
        toast.success(`Fiche trouvée: ${foundSheets[0].sheetNumber}`);
      } else {
        toast.error('Aucune fiche trouvée avec ce numéro');
        setSelectedSheet(null);
      }
    } catch (error) {
      console.error('Error searching for sheet:', error);
      toast.error('Erreur lors de la recherche');
      setSelectedSheet(null);
    } finally {
      setLoading(false);
    }
  };

  // File import functionality
  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!selectedSheet) {
      toast.error('Veuillez d\'abord rechercher et sélectionner une fiche');
      return;
    }

    setImporting(true);
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        console.log('Imported data sample:', jsonData.length > 0 ? jsonData[0] : 'No data');
        console.log('Column names:', jsonData.length > 0 ? Object.keys(jsonData[0]) : 'No columns');

        // Process and compare with subscribers
        await processImportedData(jsonData);
      } catch (error) {
        console.error('Error reading file:', error);
        toast.error('Erreur lors de la lecture du fichier');
      } finally {
        setImporting(false);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  // Process imported data and compare with subscribers
  const processImportedData = async (data) => {
    try {
      // Get subscribers for the selected sheet's circuit
      const circuitInfo = selectedSheet.circuit;
      if (!circuitInfo) {
        toast.error('Informations de circuit manquantes pour cette fiche');
        return;
      }

      console.log('Circuit info:', circuitInfo);
      console.log('Fetching subscribers for:', `groupe=${circuitInfo.groupe}, tournee=${circuitInfo.tournee}, circuit=${circuitInfo.circuit_number}`);

      // Use the circuit_number from the circuit info (should be a string like "01")
      const circuitNumber = circuitInfo.circuit_number;
      console.log('Using circuit number:', circuitNumber, 'type:', typeof circuitNumber);

      const subscribersRes = await api.get(`/api/subscribers/circuit/${circuitInfo.groupe}/${circuitInfo.tournee}/${circuitNumber}`);
      const subscribers = subscribersRes.data.data || [];

      console.log('Found subscribers:', subscribers.length);
      if (subscribers.length > 0) {
        console.log('Sample subscriber:', subscribers[0]);
      }

      // Map imported data to match with subscribers
      const processedData = data.map(row => {
        // Get reference from imported data
        const importedReference = row['Référence'] || row['Reference'] || '';

        console.log('Looking for subscriber with reference:', importedReference);

        // Find subscriber by reference
        const subscriberByReference = subscribers.find(sub => sub.reference === importedReference);

        // Since we're using DB index as old index, we just need to find the subscriber by reference
        const matchingSubscriber = subscriberByReference;

        if (subscriberByReference) {
          console.log(`Found subscriber ${subscriberByReference.reference}: DB index=${subscriberByReference.index}`);
        } else {
          console.log(`No subscriber found with reference: ${importedReference}`);
        }

        // Get new index - handle 0 as valid value, only treat null/undefined/empty string as missing
        const rawNewIndex = row['Nouvel index'] || row['New Index'];
        const newIndexValue = rawNewIndex !== undefined && rawNewIndex !== null && rawNewIndex !== '' ? rawNewIndex : '';

        // Get tarif from database if subscriber found, otherwise from file
        const tarif = subscriberByReference ? subscriberByReference.tarif : (row['TRF'] || row['Tarif'] || '');

        // Get old index (Ancien Index) from database if subscriber found, otherwise from file
        const oldIndexFromDB = subscriberByReference ? subscriberByReference.index : '';
        const oldIndexFromFile = row['Ancien index(index)'] || row['Ancien index'] || row['Last Index'] || '';
        const oldIndexValue = subscriberByReference ? oldIndexFromDB : oldIndexFromFile;

        const processedRow = {
          id: Math.random().toString(36).substring(2, 11),
          codeAgence: row['Code d\'agence'] || row['Code Agence'] || '',
          groupe: row['Groupe'] || '',
          tournee: row['Tournée'] || row['Tournee'] || '',
          circuit: row['Circuit'] || '',
          reference: importedReference,
          client: row['Client'] || '',
          adresse: row['Adresse'] || '',
          tarif: tarif,
          cad: row['CAD(dials)'] || row['CAD'] || '',
          meterNumber: row['N° de compteur'] || row['Meter Number'] || '',
          meterType: row['Type de compteur'] || row['Meter Type'] || '',
          oldIndex: oldIndexValue, // From DB if subscriber found, editable
          newIndex: newIndexValue, // Keep 0 as valid value
          salesRepRef: row['N° de attaché'] || row['Sales Rep'] || '',
          anomalies: row['Anomalies'] || row['Anomalie'] || '', // Get from file
          observation: row['Observation'] || '', // Editable
          // Database match info
          dbMatch: matchingSubscriber ? true : false,
          dbData: subscriberByReference || null,
          // Additional validation info
          referenceFound: subscriberByReference ? true : false,
          indexMatches: matchingSubscriber ? true : false
        };

        console.log('Processed row:', processedRow.reference, 'DB Match:', processedRow.dbMatch, 'Ref Found:', processedRow.referenceFound);
        return processedRow;
      });

      setImportedData(processedData);
      setEditableData(processedData);
      setShowImportTable(true);
      toast.success(`${processedData.length} lignes importées et comparées avec la base de données`);
    } catch (error) {
      console.error('Error processing imported data:', error);
      toast.error('Erreur lors du traitement des données importées');
    }
  };

  // Handle editing of table data
  const handleDataEdit = (id, field, value) => {
    setEditableData(prevData =>
      prevData.map(row =>
        row.id === id ? { ...row, [field]: value } : row
      )
    );
  };

  // Handle status toggle
  const handleStatusToggle = (id) => {
    setEditableData(prevData =>
      prevData.map(row => {
        if (row.id === id) {
          // If currently has anomalies (Non valide)
          if (row.anomalies) {
            // Check if new index is not empty
            if (row.newIndex !== '' && row.newIndex !== null && row.newIndex !== undefined) {
              // Clear anomalies to make it valid by setting anomalies to empty string
              toast.success('Statut changé à Validé');
              return { ...row, anomalies: '' };
            } else {
              // Show error if new index is empty
              toast.error('Veuillez d\'abord saisir un nouvel index avant de valider');
              return row;
            }
          } else {
            // If currently valid, make it non valid with default anomaly
            toast.info('Statut changé à Non valide');
            return { ...row, anomalies: 'Erreur' };
          }
        }
        return row;
      })
    );
  };

  // Save readings to database
  const handleSaveReadings = async () => {
    try {
      setImporting(true);

      // Prepare readings data for saving
      const readingsToSave = editableData.map(row => ({
        meterId: row.meterNumber,
        customerId: row.reference,
        customerName: row.client,
        readingValue: parseFloat(row.newIndex) || 0,
        readingSheet: selectedSheet._id,
        notes: row.observation,
        validationMode: 'offline'
      }));

      // Save all readings
      const savePromises = readingsToSave.map(reading =>
        api.post('/api/readings', reading)
      );

      await Promise.all(savePromises);

      toast.success(`${readingsToSave.length} relevés sauvegardés avec succès`);

      // Reset import state
      setShowImportTable(false);
      setImportedData([]);
      setEditableData([]);
      setSelectedSheet(null);
      setSearchTerm('');

    } catch (error) {
      console.error('Error saving readings:', error);
      toast.error('Erreur lors de la sauvegarde des relevés');
    } finally {
      setImporting(false);
    }
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="flex items-center justify-center h-full min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="space-y-4">


        {/* Search Section */}
        <div className="bg-white border border-gray-200 shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
            <FiSearch className="mr-2 h-5 w-5 text-primary-600" />
            Rechercher une Fiche de Relevé
          </h3>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                Numéro de Fiche
              </label>
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Entrer le numéro de fiche..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <button
              onClick={handleSearch}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200 rounded-md"
            >
              <FiSearch className="mr-2 h-4 w-4" />
              Rechercher
            </button>
          </div>

          {/* Selected Sheet Display */}
          {selectedSheet && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h4 className="text-sm font-medium text-green-800 mb-2">Fiche Sélectionnée</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Numéro:</span>
                  <span className="ml-2 text-gray-900">{selectedSheet.sheetNumber}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Zone:</span>
                  <span className="ml-2 text-gray-900">{selectedSheet.area}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Assigné à:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedSheet.assignedTo ? `${selectedSheet.assignedTo.firstName} ${selectedSheet.assignedTo.lastName}` : 'Non assigné'}
                  </span>
                </div>
              </div>

              {/* File Import Section */}
              <div className="mt-4 pt-4 border-t border-green-200">
                <label htmlFor="file-import" className="block text-sm font-medium text-gray-700 mb-2">
                  Importer le fichier de relevés (CSV/Excel)
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="file"
                    id="file-import"
                    accept=".csv,.xlsx,.xls"
                    onChange={handleFileImport}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                    disabled={importing}
                  />
                  {importing && (
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary-500 mr-2"></div>
                      Importation en cours...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>



        {/* Import Table */}
        {showImportTable && (
          <div className="bg-white border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-wrap justify-between items-center gap-4">
                <div className="flex items-center gap-4 flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-800 flex items-center whitespace-nowrap">
                    <FiUpload className="mr-2 h-5 w-5 text-primary-600" />
                    Données Importées - {filteredData.length} relevés
                    {filteredData.length !== editableData.length && (
                      <span className="ml-2 text-sm text-gray-500">
                        (sur {editableData.length} total)
                      </span>
                    )}
                  </h3>
                  {/* Clear Filters Button */}
                  {(showOnlyAnomalies || clientFilter) && (
                    <button
                      onClick={() => {
                        setShowOnlyAnomalies(false);
                        setClientFilter('');
                      }}
                      className="ml-4 px-3 py-1 text-xs bg-gray-100 text-gray-700 hover:bg-gray-200 rounded flex items-center"
                    >
                      <FiX className="mr-1 h-3 w-3" />
                      Effacer filtres
                    </button>
                  )}
                  {/* Pagination Controls Top (inline with title) */}
                  <div className="flex-1 flex justify-center">
                    {renderPagination()}
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowImportTable(false)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200 rounded-md"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleSaveReadings}
                    disabled={importing}
                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200 rounded-md disabled:opacity-50"
                  >
                    {importing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Sauvegarde...
                      </>
                    ) : (
                      <>
                        <FiSave className="mr-2 h-4 w-4" />
                        Sauvegarder les Relevés
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-32">Référence</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-48">
                      <div className="flex items-center justify-between">
                        <span>Client</span>
                        <div className="relative w-24">
                          <input
                            type="text"
                            value={clientFilter}
                            onChange={(e) => setClientFilter(e.target.value)}
                            placeholder=""
                            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                          />
                          <FiSearch className="absolute right-2 top-1 h-3 w-3 text-gray-400" />
                          {clientFilter && (
                            <button
                              onClick={() => setClientFilter('')}
                              className="absolute right-6 top-1 p-0.5 hover:bg-gray-200 rounded"
                            >
                              <FiX className="h-2 w-2" />
                            </button>
                          )}
                        </div>
                      </div>
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-20">Tarif</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-32">N° Compteur</th>
                    <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-56">Index</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-20">Statut</th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase border-r border-gray-300 w-40">
                      <button
                        onClick={() => setShowOnlyAnomalies(!showOnlyAnomalies)}
                        className={`flex items-center justify-between w-full p-1 rounded transition-colors ${
                          showOnlyAnomalies ? 'bg-red-100 text-red-800' : 'hover:bg-gray-200'
                        }`}
                        title={showOnlyAnomalies ? 'Afficher tous les clients' : 'Afficher seulement les clients avec anomalies'}
                      >
                        <span>Anomalies</span>
                        <FiFilter className={`h-3 w-3 ${showOnlyAnomalies ? 'text-red-600' : 'text-gray-400'}`} />
                      </button>
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Observation</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedEditableData.map((row, idx) => (
                    <tr
                      key={row.id}
                      className={`hover:bg-gray-50${idx === paginatedEditableData.length - 1 ? ' border-b border-gray-300' : ''}`}
                    >
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <div className="text-sm font-medium text-gray-800">{row.reference}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <div className="text-sm text-gray-700">{row.client}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <span className="inline-flex px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
                          {row.tarif}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <div className="text-sm text-gray-700">{row.meterNumber}</div>
                      </td>
                      {/* Index mini table */}
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <div className="inline-block">
                          <div className="flex border border-gray-200 rounded">
                            <div className="w-16 px-2 py-1 text-center border-r border-gray-200">
                              <div className="text-xs font-medium text-gray-500">Cad</div>
                              <div className="flex justify-center">
                                <span className="inline-block min-w-[32px] px-2 py-1 bg-white border border-gray-200 rounded text-xs text-gray-700">
                                  {row.cad || '1'}
                                </span>
                              </div>
                            </div>
                            <div className="w-16 px-2 py-1 text-center border-r border-gray-200">
                              <div className="text-xs font-medium text-gray-500">AI</div>
                              <div className="flex justify-center">
                                <span className="inline-block min-w-[32px] px-2 py-1 bg-white border border-gray-200 rounded text-xs text-gray-700">
                                  {row.oldIndex}
                                </span>
                              </div>
                            </div>
                            <div className="w-16 px-2 py-1 text-center">
                              <div className="text-xs font-medium text-gray-500">NI</div>
                              <div className="flex justify-center">
                                <input
                                  type="number"
                                  inputMode="numeric"
                                  pattern="[0-9]*"
                                  value={row.newIndex}
                                  onChange={(e) => {
                                    // Only allow numbers
                                    const val = e.target.value;
                                    if (/^\d*$/.test(val)) {
                                      handleDataEdit(row.id, 'newIndex', val);
                                    }
                                  }}
                                  className="inline-block min-w-[32px] px-2 py-1 bg-white border border-gray-200 rounded text-xs text-center focus:outline-none focus:ring-1 focus:ring-primary-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                  placeholder=""
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        <div className="flex flex-col gap-1">
                          <button
                            onClick={() => handleStatusToggle(row.id)}
                            disabled={row.anomalies && (!row.newIndex || row.newIndex === '')}
                            title={row.anomalies && (!row.newIndex || row.newIndex === '') ? 'Veuillez d\'abord saisir un nouvel index avant de valider' : ''}
                            className={`inline-flex px-2 py-1 text-xs font-medium
                              ${row.anomalies
                                ? (!row.newIndex || row.newIndex === '')
                                  ? 'bg-red-100 text-red-800 opacity-60 cursor-not-allowed'
                                  : 'bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer'
                              } rounded transition-colors`}
                          >
                            {row.anomalies ? 'Non validé' : row.dbMatch ? 'Validé' : row.referenceFound ? 'Index différent' : 'Référence introuvable'}
                          </button>
                          {row.referenceFound && !row.dbMatch && row.dbData && (
                            <span className="text-xs text-gray-500">
                              DB: {row.dbData.index} | Fichier: {row.oldIndex}
                            </span>
                          )}
                          {row.anomalies && (!row.newIndex || row.newIndex === '') && (
                            <span className="text-xs text-red-500 mt-1">
                              Saisir NI pour valider
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap border-r border-gray-300">
                        {row.anomalies === '' ? (
                          <span className="text-xs text-gray-400">&nbsp;</span>
                        ) : (
                          <select
                            value={row.anomalies}
                            onChange={(e) => handleDataEdit(row.id, 'anomalies', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                          >
                            <option value="Capteur Bloqué">Capteur Bloqué</option>
                            <option value="Capteur Changé Non Intégré">Capteur Changé Non Intégré</option>
                            <option value="Capteur Décollé">Capteur Décollé</option>
                            <option value="Capteur Défectueux">Capteur Défectueux</option>
                            <option value="Capteur N'affiche Pas">Capteur N'affiche Pas</option>
                            <option value="Déplombé">Déplombé</option>
                            <option value="Capteur Déposé">Capteur Déposé</option>
                            <option value="Non Localisé">Non Localisé</option>
                            <option value="Non Relevé">Non Relevé</option>
                            <option value="Panneau Décollé">Panneau Décollé</option>
                            <option value="Pas Erreur">Pas Erreur</option>
                            <option value="Pas Causé">Pas Causé</option>
                            <option value="Tailles Inexactes">Tailles Inexactes</option>
                            <option value="Erreur">Erreur</option>
                            <option value="Capteur Défaut">Capteur Défaut</option>
                            <option value="Capteur Non Intégré">Capteur Non Intégré</option>
                            {/* Show imported value if it's not in the predefined list */}
                            {row.anomalies && !['Capteur Bloqué', 'Capteur Changé Non Intégré', 'Capteur Décollé', 'Capteur Défectueux', 'Capteur N\'affiche Pas', 'Déplombé', 'Capteur Déposé', 'Non Localisé', 'Non Relevé', 'Panneau Décollé', 'Pas Erreur', 'Pas Causé', 'Tailles Inexactes', 'Erreur', 'Capteur Défaut', 'Capteur Non Intégré'].includes(row.anomalies) && (
                              <option value={row.anomalies}>{row.anomalies} (Importé)</option>
                            )}
                          </select>
                        )}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {row.anomalies === '' ? (
                          <span className="text-xs text-gray-400">&nbsp;</span>
                        ) : (
                          <input
                            type="text"
                            value={row.observation}
                            onChange={(e) => handleDataEdit(row.id, 'observation', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            placeholder="Observation"
                          />
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {/* Pagination Controls Bottom */}
            {renderPagination()}
          </div>
        )}

        {/* Main Content */}
        {!showImportTable && (
          <div className="bg-white border border-gray-200 overflow-hidden shadow-sm">
            {readingSheets.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="bg-blue-50 rounded-full p-3 mb-4">
                  <FiAlertCircle className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Aucune fiche à recevoir</h3>
                <p className="text-gray-500 text-center max-w-md mb-4">
                  Il n'y a pas de fiches de relevé assignées disponibles pour la réception.
                </p>
                <Link
                  to="/reading-sheets"
                  className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                >
                  <FiFileText className="mr-2 h-4 w-4" />
                  Voir toutes les fiches
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Numéro</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Zone</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Assigné à</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date d'assignation</th>
                      <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {readingSheets.map(sheet => (
                      <tr key={sheet._id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <FiFileText className="h-4 w-4 text-gray-400 mr-2" />
                            <div className="text-sm font-medium text-gray-800">{sheet.sheetNumber}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <FiMapPin className="h-4 w-4 text-gray-400 mr-2" />
                            <div className="text-sm text-gray-700">{sheet.area}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <FiUser className="h-4 w-4 text-gray-400 mr-2" />
                            <div className="text-sm text-gray-700">
                              {sheet.assignedTo ? sheet.assignedTo.name || `${sheet.assignedTo.firstName || ''} ${sheet.assignedTo.lastName || ''}`.trim() : 'Non assignée'}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm text-gray-700">
                            {sheet.assignedDate
                              ? new Date(sheet.assignedDate).toLocaleDateString('fr-FR')
                              : 'N/A'}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right">
                          <button
                            onClick={() => markAsCompleted(sheet._id)}
                            className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium hover:bg-green-700 transition-colors duration-200"
                          >
                            <FiCheckCircle className="mr-1.5 h-4 w-4" />
                            Marquer comme complétée
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </ModernLayout>
  );
};

export default ReceiveReadingSheets;
