const express = require('express');
const {
  getSubscribers,
  getSubscribersByCircuit,
  getSubscribersByCircuits,
  getSubscriberByReference
} = require('../controllers/subscriberController');
const { protect } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.route('/')
  .get(getSubscribers);

router.route('/circuit/:groupe/:tournee/:circuit')
  .get(getSubscribersByCircuit);

router.route('/circuits')
  .post(getSubscribersByCircuits);

router.route('/reference/:reference')
  .get(getSubscriberByReference);

module.exports = router;
