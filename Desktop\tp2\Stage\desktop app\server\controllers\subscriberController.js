const Subscriber = require('../models/Subscriber');

// @desc    Get subscribers by circuit
// @route   GET /api/subscribers/circuit/:groupe/:tournee/:circuit
// @access  Private
exports.getSubscribersByCircuit = async (req, res) => {
  try {
    const { groupe, tournee, circuit } = req.params;
    
    const subscribers = await Subscriber.find({
      groupe: parseInt(groupe),
      tournee: parseInt(tournee),
      circuit_number: circuit
    });

    res.status(200).json({
      success: true,
      count: subscribers.length,
      data: subscribers
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get subscribers by multiple circuits
// @route   POST /api/subscribers/circuits
// @access  Private
exports.getSubscribersByCircuits = async (req, res) => {
  try {
    const { circuits } = req.body;
    
    if (!circuits || !Array.isArray(circuits)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of circuits'
      });
    }

    // Build query for multiple circuits
    const circuitQueries = circuits.map(circuit => ({
      groupe: circuit.groupe,
      tournee: circuit.tournee,
      circuit_number: circuit.circuit_number
    }));

    const subscribers = await Subscriber.find({
      $or: circuitQueries
    });

    res.status(200).json({
      success: true,
      count: subscribers.length,
      data: subscribers
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get all subscribers
// @route   GET /api/subscribers
// @access  Private
exports.getSubscribers = async (req, res) => {
  try {
    const subscribers = await Subscriber.find();

    res.status(200).json({
      success: true,
      count: subscribers.length,
      data: subscribers
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get subscriber by reference
// @route   GET /api/subscribers/reference/:reference
// @access  Private
exports.getSubscriberByReference = async (req, res) => {
  try {
    const subscriber = await Subscriber.findOne({ reference: req.params.reference });

    if (!subscriber) {
      return res.status(404).json({
        success: false,
        message: 'Subscriber not found'
      });
    }

    res.status(200).json({
      success: true,
      data: subscriber
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
