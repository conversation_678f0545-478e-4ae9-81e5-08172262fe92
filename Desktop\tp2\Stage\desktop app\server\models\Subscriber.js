const mongoose = require('mongoose');

const SubscriberSchema = new mongoose.Schema({
  direction: {
    type: String,
    required: [true, 'Please add a direction'],
    trim: true
  },
  agence: {
    type: String,
    required: [true, 'Please add an agence'],
    trim: true
  },
  groupe: {
    type: Number,
    required: [true, 'Please add a groupe']
  },
  tournee: {
    type: Number,
    required: [true, 'Please add a tournee']
  },
  circuit_number: {
    type: String,
    required: [true, 'Please add a circuit number'],
    trim: true
  },
  reference: {
    type: String,
    required: [true, 'Please add a reference'],
    trim: true
  },
  full_name: {
    type: String,
    required: [true, 'Please add a full name'],
    trim: true
  },
  adress: {
    type: String,
    required: [true, 'Please add an address'],
    trim: true
  },
  dials: {
    type: Number,
    required: [true, 'Please add dials count']
  },
  tarif: {
    type: String,
    required: [true, 'Please add a tarif'],
    trim: true
  },
  meter_number: {
    type: String,
    required: [true, 'Please add a meter number'],
    trim: true
  },
  index: {
    type: Number,
    required: [true, 'Please add an index']
  },
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Create compound index for efficient querying
SubscriberSchema.index({ groupe: 1, tournee: 1, circuit_number: 1 });
SubscriberSchema.index({ reference: 1 });
SubscriberSchema.index({ meter_number: 1 });

// Explicitly specify the collection name to match the one in MongoDB
module.exports = mongoose.model('Subscriber', SubscriberSchema, 'subscribers');
